import { useState, useCallback } from "react";
import { useEditor } from "../context/EditorContext";
import FileUploadZone from "./FileUploadZone";
import MultiSchemaValidator from "../utils/MultiSchemaValidator";
import skeletonTasksSchema from "../data/skeleton_tasks_schema.json";

const EditorTaskUpload = () => {
  const { addTask, addMultipleTasks, currentRoadmap } = useEditor();
  const [uploadStatus, setUploadStatus] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedPhase, setSelectedPhase] = useState("");

  // Get available phases
  const phases = currentRoadmap?.roadmap?.phases || currentRoadmap?.roadmap || [];

  const handleFileUpload = useCallback(async (files) => {
    if (!files || files.length === 0) return;
    if (!selectedPhase) {
      setUploadStatus({
        type: "error",
        message: "Please select a target phase before uploading tasks.",
      });
      return;
    }

    setIsProcessing(true);
    setUploadStatus(null);

    try {
      const validator = new MultiSchemaValidator({
        tasks: skeletonTasksSchema,
      });

      const results = [];
      let totalTasks = 0;
      let successfulTasks = 0;

      for (const file of files) {
        try {
          const fileContent = await file.text();
          const taskData = JSON.parse(fileContent);

          // Validate the task file
          const validation = validator.validateTasks(taskData);
          if (!validation.isValid) {
            results.push({
              fileName: file.name,
              success: false,
              error: `Validation failed: ${validation.errors.join(", ")}`,
            });
            continue;
          }

          // Process each task in the file
          if (taskData.tasks && Array.isArray(taskData.tasks)) {
            for (const task of taskData.tasks) {
              totalTasks++;
              
              // Override phase_id with selected phase
              const taskWithPhase = {
                ...task,
                phase_id: selectedPhase,
              };

              const result = addTask(selectedPhase, taskWithPhase);
              if (result.success) {
                successfulTasks++;
              } else {
                results.push({
                  fileName: file.name,
                  taskId: task.task_id,
                  success: false,
                  error: result.error || "Failed to add task",
                });
              }
            }

            results.push({
              fileName: file.name,
              success: true,
              tasksAdded: taskData.tasks.length,
            });
          } else {
            results.push({
              fileName: file.name,
              success: false,
              error: "File does not contain a valid tasks array",
            });
          }
        } catch (error) {
          results.push({
            fileName: file.name,
            success: false,
            error: `File processing error: ${error.message}`,
          });
        }
      }

      // Set upload status
      const hasErrors = results.some(r => !r.success);
      setUploadStatus({
        type: hasErrors ? "warning" : "success",
        message: `Processed ${files.length} file(s). Added ${successfulTasks}/${totalTasks} tasks successfully.`,
        details: results,
      });

    } catch (error) {
      setUploadStatus({
        type: "error",
        message: `Upload failed: ${error.message}`,
      });
    } finally {
      setIsProcessing(false);
    }
  }, [addTask, selectedPhase]);

  const handlePhaseChange = (e) => {
    setSelectedPhase(e.target.value);
    setUploadStatus(null);
  };

  const clearStatus = () => {
    setUploadStatus(null);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Add Tasks
      </h3>

      {/* Phase Selection */}
      <div className="mb-4">
        <label htmlFor="phase-select" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Target Phase
        </label>
        <select
          id="phase-select"
          value={selectedPhase}
          onChange={handlePhaseChange}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Select a phase...</option>
          {phases.map((phase) => (
            <option key={phase.phase_id} value={phase.phase_id}>
              {phase.phase_title}
            </option>
          ))}
        </select>
      </div>

      {/* File Upload Zone */}
      <FileUploadZone
        onFilesSelected={handleFileUpload}
        acceptedTypes=".json"
        maxFiles={10}
        disabled={!selectedPhase || isProcessing}
        title="Upload Task Files"
        description={
          selectedPhase
            ? "Drop JSON task files here or click to browse"
            : "Select a target phase first"
        }
      />

      {/* Processing Indicator */}
      {isProcessing && (
        <div className="mt-4 flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
          <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-3"></div>
          <span className="text-blue-700 dark:text-blue-300">Processing task files...</span>
        </div>
      )}

      {/* Upload Status */}
      {uploadStatus && (
        <div className={`mt-4 p-4 rounded-md ${
          uploadStatus.type === "success"
            ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800"
            : uploadStatus.type === "warning"
            ? "bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800"
            : "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800"
        }`}>
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <p className={`font-medium ${
                uploadStatus.type === "success"
                  ? "text-green-800 dark:text-green-200"
                  : uploadStatus.type === "warning"
                  ? "text-yellow-800 dark:text-yellow-200"
                  : "text-red-800 dark:text-red-200"
              }`}>
                {uploadStatus.message}
              </p>

              {/* Detailed Results */}
              {uploadStatus.details && uploadStatus.details.length > 0 && (
                <div className="mt-3 space-y-2">
                  {uploadStatus.details.map((result, index) => (
                    <div key={index} className="text-sm">
                      <span className="font-medium">{result.fileName}:</span>
                      {result.success ? (
                        <span className="text-green-700 dark:text-green-300 ml-2">
                          ✓ {result.tasksAdded} task(s) added
                        </span>
                      ) : (
                        <span className="text-red-700 dark:text-red-300 ml-2">
                          ✗ {result.error}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            <button
              onClick={clearStatus}
              className="ml-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
        <p className="mb-2">
          <strong>Supported format:</strong> JSON files with task arrays following the skeleton tasks schema.
        </p>
        <p>
          Tasks will be added to the selected phase. Existing task IDs will be validated for uniqueness.
        </p>
      </div>
    </div>
  );
};

export default EditorTaskUpload;
